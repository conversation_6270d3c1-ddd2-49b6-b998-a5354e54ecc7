<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

/**
 * Middleware to inject copy functionality into Laravel Debugbar
 * by modifying the HTML response after debugbar has been rendered.
 */
final class InjectDebugbarCopy
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);



        // Only inject for HTML responses in debug mode when debugbar is likely present
        if (!config('app.debug') ||
            !$response instanceof Response ||
            !str_contains($response->headers->get('Content-Type', ''), 'text/html') ||
            !str_contains($response->getContent(), 'phpdebugbar')) {
            return $response;
        }

        try {
            $content = $response->getContent();
            $injectedContent = $this->injectCopyFunctionality($content);
            $response->setContent($injectedContent);
        } catch (\Throwable $e) {
            // Fail silently to never break the app
        }

        return $response;
    }

    private function injectCopyFunctionality(string $content): string
    {
        $copyScript = $this->getCopyScript();
        $copyStyles = $this->getCopyStyles();

        // Inject before closing body tag
        $injection = $copyStyles . "\n" . $copyScript;
        
        return str_replace('</body>', $injection . "\n</body>", $content);
    }

    private function getCopyStyles(): string
    {
        return '<style>
.phpdebugbar-copy-btn, .phpdebugbar-copy-all-btn {
    background: #333 !important; color: #fff !important; border: none !important;
    padding: 4px 8px !important; margin: 0 4px !important; border-radius: 3px !important;
    cursor: pointer !important; font-size: 11px !important; font-family: inherit !important;
}
.phpdebugbar-copy-all-btn { background: #e74c3c !important; margin-right: 5px !important; }
.phpdebugbar-copy-success { background: #27ae60 !important; }
.phpdebugbar-copy-error { background: #e74c3c !important; }
</style>';
    }

    private function getCopyScript(): string
    {
        return '<script>
(function() {
    function copyToClipboard(text) {
        if (navigator.clipboard) {
            return navigator.clipboard.writeText(text).then(() => true).catch(() => false);
        }
        const textArea = document.createElement("textarea");
        textArea.value = text; 
        textArea.style.position = "fixed"; 
        textArea.style.left = "-999999px";
        document.body.appendChild(textArea); 
        textArea.select();
        try { 
            const result = document.execCommand("copy"); 
            document.body.removeChild(textArea); 
            return result; 
        } catch(e) { 
            document.body.removeChild(textArea); 
            return false; 
        }
    }

    function showFeedback(btn, success) {
        const orig = btn.textContent;
        btn.textContent = success ? "✓" : "✗";
        btn.className += success ? " phpdebugbar-copy-success" : " phpdebugbar-copy-error";
        setTimeout(() => {
            btn.textContent = orig;
            btn.className = btn.className.replace(" phpdebugbar-copy-success", "").replace(" phpdebugbar-copy-error", "");
        }, 2000);
    }

    function filterPanelData(panelName, data) {
        if (!data) return data;

        switch (panelName) {
            case "views":
                return filterViewsData(data);
            case "queries":
                return filterQueriesData(data);
            case "request":
                return filterRequestData(data);
            case "route":
                return filterRouteData(data);
            case "messages":
                return filterMessagesData(data);
            case "timeline":
                return filterTimelineData(data);
            case "models":
                return filterModelsData(data);
            case "files":
                return filterFilesData(data);
            case "auth":
                return filterAuthData(data);
            case "config":
                return filterConfigData(data);
            case "session":
                return filterSessionData(data);
            case "events":
                return filterEventsData(data);
            default:
                return data; // Return as-is for unknown panels
        }
    }

    function filterViewsData(data) {
        if (Array.isArray(data)) {
            return data.map(view => {
                if (typeof view === "object" && view !== null) {
                    const filtered = { ...view };
                    // Remove unwanted fields as requested
                    delete filtered.template_type;
                    delete filtered.relative_file_path;
                    // Also remove empty relative_file_path if it exists
                    if (filtered.relative_file_path === "") {
                        delete filtered.relative_file_path;
                    }
                    return filtered;
                }
                return view;
            });
        }
        return data;
    }

    function filterQueriesData(data) {
        // Ensure we get complete query data including SQL, bindings, time, etc.
        if (Array.isArray(data)) {
            return data.map(query => {
                if (typeof query === "object" && query !== null) {
                    return {
                        sql: query.sql || "",
                        bindings: query.bindings || [],
                        time: query.time || query.duration || "",
                        connection: query.connection || "default",
                        file: query.file || "",
                        line: query.line || ""
                    };
                }
                return query;
            });
        }
        return data;
    }

    function filterRequestData(data) {
        // Ensure complete request data is captured
        return data;
    }

    function filterRouteData(data) {
        // Ensure complete route data is captured
        return data;
    }

    function filterMessagesData(data) {
        // Ensure complete messages/logs data is captured
        if (Array.isArray(data)) {
            return data.map(message => {
                if (typeof message === "object" && message !== null) {
                    return {
                        level: message.level || "",
                        message: message.message || "",
                        context: message.context || {},
                        timestamp: message.timestamp || message.time || ""
                    };
                }
                return message;
            });
        }
        return data;
    }

    function filterTimelineData(data) {
        // Ensure complete timeline data is captured
        return data;
    }

    function filterModelsData(data) {
        // Ensure complete models data is captured
        return data;
    }

    function filterFilesData(data) {
        // Ensure complete files data is captured with relative paths
        if (Array.isArray(data)) {
            return data.map(file => {
                if (typeof file === "string") {
                    // Convert absolute paths to relative paths for better readability
                    return file.replace(/^.*?(app\/|resources\/|config\/|database\/|routes\/|public\/|storage\/|tests\/|vendor\/|Modules\/)/, "$1");
                }
                return file;
            });
        }
        return data;
    }

    function filterAuthData(data) {
        // Ensure complete auth data is captured
        return data;
    }

    function filterConfigData(data) {
        // Ensure complete config data is captured
        return data;
    }

    function filterSessionData(data) {
        // Ensure complete session data is captured
        return data;
    }

    function filterEventsData(data) {
        // For events, we will let the JavaScript extraction handle the processing
        // since events data structure can be complex
        return data;
    }

    function extractPanelData(panelName) {
        const panel = document.querySelector(`[data-collector="${panelName}"]`);
        if (!panel) return {error: "Panel not found"};

        // Try to get the actual debugbar data from the global debugbar object
        let detailedData = null;
        if (window.phpdebugbar && window.phpdebugbar.datasets && window.phpdebugbar.datasets.length > 0) {
            const dataset = window.phpdebugbar.datasets[window.phpdebugbar.datasets.length - 1];
            if (dataset && dataset[panelName]) {
                detailedData = dataset[panelName];
            }
        }

        // Make output more LLM-friendly with descriptive terms
        const friendlyNames = {
            "views": "blade_views_context",
            "session": "session_data_details",
            "request": "request_context_details",
            "queries": "database_queries_context",
            "models": "eloquent_models_context",
            "logs": "application_logs_context",
            "files": "loaded_files_context",
            "auth": "authentication_context",
            "config": "configuration_context",
            "events": "application_events_context",
            "timeline": "execution_timeline_context",
            "messages": "debug_messages_context",
            "route": "routing_context"
        };

        const contextType = friendlyNames[panelName] || panelName + "_context";

        // If we have detailed data, use it; otherwise fall back to parsing the panel content
        if (detailedData) {
            // Apply panel-specific filtering to clean up the data
            const filteredData = filterPanelData(panelName, detailedData);

            return {
                context_type: contextType,
                captured_at: new Date().toISOString(),
                context_data: filteredData
            };
        }

        // Fallback: try to extract structured data from the panel HTML
        const structuredData = extractStructuredDataFromPanel(panel, panelName);

        return {
            context_type: contextType,
            captured_at: new Date().toISOString(),
            context_data: structuredData || panel.textContent.trim()
        };
    }

    function extractStructuredDataFromPanel(panel, panelName) {
        try {
            // Find the actual panel content, not just the tab button
            const panelContent = document.querySelector(`.phpdebugbar-panel[data-collector="${panelName}"]`);
            if (!panelContent) {
                console.error(`Panel content not found: ${panelName}`);
                return null;
            }

            switch (panelName) {
                case "models":
                    return extractModelsData(panelContent);
                case "views":
                    return extractViewsData(panelContent);
                case "queries":
                    return extractQueriesData(panelContent);
                case "route":
                    return extractRouteData(panelContent);
                case "request":
                    return extractRequestData(panelContent);
                case "messages":
                    return extractMessagesData(panelContent);
                case "events":
                    return extractEventsData(panelContent);
                default:
                    return extractGenericData(panelContent);
            }
        } catch (e) {
            console.warn("Failed to extract structured data for " + panelName + ":", e);
            return null;
        }
    }

    function extractModelsData(panel) {
        const models = [];
        const dtElements = panel.querySelectorAll("dt.phpdebugbar-widgets-key");
        const ddElements = panel.querySelectorAll("dd.phpdebugbar-widgets-value");

        for (let i = 0; i < dtElements.length && i < ddElements.length; i++) {
            const modelName = dtElements[i].textContent.trim();
            const ddText = ddElements[i].textContent.trim();
            // Extract just the count number (before the filename)
            const countMatch = ddText.match(new RegExp("^(\\d+)"));
            const count = countMatch ? countMatch[1] : ddText;

            if (modelName && count) {
                models.push({
                    model: modelName,
                    count: count
                });
            }
        }
        return models.length > 0 ? models : null;
    }

    function extractViewsData(panel) {
        const views = [];
        const listItems = panel.querySelectorAll(".phpdebugbar-widgets-list-item");
        listItems.forEach(item => {
            const nameEl = item.querySelector(".phpdebugbar-widgets-name");
            const paramCountEl = item.querySelector(".phpdebugbar-widgets-param-count");

            if (nameEl) {
                const viewData = {
                    blade_template: nameEl.textContent.trim(),
                    template_parameters: paramCountEl ? paramCountEl.textContent.trim() : "0"
                };

                views.push(viewData);
            }
        });
        return views.length > 0 ? views : null;
    }

    function extractQueriesData(panel) {
        const queries = [];

        // First, try to expand any collapsible sections to get full query details
        const expandableButtons = panel.querySelectorAll("a[class*=sf-dump-toggle], .phpdebugbar-widgets-toggle");
        expandableButtons.forEach(button => {
            try {
                if (button.style.display !== "none" && !button.classList.contains("sf-dump-expanded")) {
                    button.click();
                }
            } catch (e) {
                // Ignore click errors
            }
        });

        // Extract from list items (li elements) which contain individual queries
        const queryListItems = panel.querySelectorAll("li");
        queryListItems.forEach((listItem, index) => {
            // Extract filename and line number
            const filenameEl = listItem.querySelector("generic[title*=Filename], .phpdebugbar-widgets-key");
            let filename = "";
            if (filenameEl) {
                filename = filenameEl.textContent.trim();
            } else {
                // Try alternative selectors for filename
                const filenameLink = listItem.querySelector("a[href*=cursor]");
                if (filenameLink) {
                    const filenameText = filenameLink.previousElementSibling;
                    if (filenameText) {
                        filename = filenameText.textContent.trim();
                    }
                }
            }

            // Extract connection
            let connection = "";
            const connectionEl = listItem.querySelector("generic[title*=Connection]");
            if (connectionEl) {
                connection = connectionEl.textContent.trim();
            }

            // Extract duration
            let duration = "";
            const durationEl = listItem.querySelector("generic[title*=Duration]");
            if (durationEl) {
                duration = durationEl.textContent.trim();
            }

            // Extract SQL query from code element
            let sql = "";
            const codeEl = listItem.querySelector("code");
            if (codeEl) {
                sql = codeEl.textContent.trim();
            }

            // If we found SQL, add this query to our results
            if (sql && sql.length > 0) {
                queries.push({
                    index: index + 1,
                    filename: filename || "Unknown",
                    connection: connection || "Unknown",
                    duration: duration || "Unknown",
                    sql: sql
                });
            }
        });

        // Fallback: try the original method if no queries found
        if (queries.length === 0) {
            const queryElements = panel.querySelectorAll(".phpdebugbar-widgets-sql");
            queryElements.forEach((element, index) => {
                const sqlEl = element.querySelector(".phpdebugbar-widgets-sql-sql");
                const durationEl = element.querySelector(".phpdebugbar-widgets-sql-duration");
                const connectionEl = element.querySelector(".phpdebugbar-widgets-sql-connection");

                const sql = sqlEl ? sqlEl.textContent.trim() : "";
                const duration = durationEl ? durationEl.textContent.trim() : "";
                const connection = connectionEl ? connectionEl.textContent.trim() : "";

                if (sql) {
                    queries.push({
                        index: index + 1,
                        filename: "Unknown",
                        sql: sql,
                        duration: duration || "Unknown",
                        connection: connection || "Unknown"
                    });
                }
            });
        }

        return queries.length > 0 ? queries : null;
    }

    function extractRouteData(panel) {
        const routeData = {};

        // First, try to expand all collapsible sections to get full data
        const expandableButtons = panel.querySelectorAll("a[class*=sf-dump-toggle], .phpdebugbar-widgets-toggle");
        expandableButtons.forEach(button => {
            try {
                if (button.style.display !== "none" && !button.classList.contains("sf-dump-expanded")) {
                    button.click();
                }
            } catch (e) {
                // Ignore click errors
            }
        });

        // Extract from table rows (tr/td structure)
        const rows = panel.querySelectorAll("tr");
        rows.forEach(row => {
            const cells = row.querySelectorAll("td");
            if (cells.length >= 2) {
                const key = cells[0] ? cells[0].textContent.trim() : "";
                const value = cells[1] ? cells[1].textContent.trim() : "";
                if (key && value) {
                    routeData[key] = value;
                }
            }
        });

        // Extract from definition lists (dt/dd structure)
        const terms = panel.querySelectorAll("dt");
        terms.forEach(term => {
            const definition = term.nextElementSibling;
            if (definition && definition.tagName === "DD") {
                const key = term.textContent.trim();
                let value = definition.textContent.trim();

                // Handle expandable content (arrays/objects) - get full expanded text
                const expandableElements = definition.querySelectorAll(".sf-dump, .phpdebugbar-widgets-kvlist");
                if (expandableElements.length > 0) {
                    expandableElements.forEach(expandable => {
                        const expandableText = expandable.textContent.trim();
                        if (expandableText && expandableText.length > value.length) {
                            value = expandableText;
                        }
                    });
                }

                // Also check for specific cookie/header data patterns
                const cookieElements = definition.querySelectorAll(".sf-dump-str");
                if (cookieElements.length > 0) {
                    let cookieValue = "";
                    cookieElements.forEach(cookieEl => {
                        const cookieText = cookieEl.textContent.trim();
                        if (cookieText && cookieText.length > cookieValue.length) {
                            cookieValue = cookieText;
                        }
                    });
                    if (cookieValue && cookieValue.length > value.length) {
                        value = cookieValue;
                    }
                }

                if (key && value) {
                    routeData[key] = value;
                }
            }
        });

        // Extract from any other key-value pairs in the panel
        const kvPairs = panel.querySelectorAll(".phpdebugbar-widgets-kvlist li");
        kvPairs.forEach(item => {
            const keyElement = item.querySelector(".phpdebugbar-widgets-key");
            const valueElement = item.querySelector(".phpdebugbar-widgets-value");
            if (keyElement && valueElement) {
                const key = keyElement.textContent.trim();
                const value = valueElement.textContent.trim();
                if (key && value) {
                    routeData[key] = value;
                }
            }
        });

        return Object.keys(routeData).length > 0 ? routeData : null;
    }

    function extractRequestData(panel) {
        const requestData = {};
        const sections = panel.querySelectorAll(".phpdebugbar-widgets-kvlist");
        sections.forEach(section => {
            const titleEl = section.previousElementSibling;
            const title = titleEl ? titleEl.textContent.trim() : "";
            const items = {};
            const rows = section.querySelectorAll("tr");
            rows.forEach(row => {
                const cells = row.querySelectorAll("td");
                if (cells.length >= 2) {
                    const key = cells[0] ? cells[0].textContent.trim() : "";
                    const value = cells[1] ? cells[1].textContent.trim() : "";
                    if (key && value) {
                        items[key] = value;
                    }
                }
            });
            if (title && Object.keys(items).length > 0) {
                requestData[title] = items;
            }
        });
        return Object.keys(requestData).length > 0 ? requestData : null;
    }

    function extractMessagesData(panel) {
        const messages = [];
        const messageElements = panel.querySelectorAll(".phpdebugbar-widgets-list-item");
        messageElements.forEach(element => {
            const levelEl = element.querySelector(".phpdebugbar-widgets-log-level");
            const messageEl = element.querySelector(".phpdebugbar-widgets-log-message");
            const contextEl = element.querySelector(".phpdebugbar-widgets-log-context");

            const level = levelEl ? levelEl.textContent.trim() : "";
            const message = messageEl ? messageEl.textContent.trim() : "";
            const context = contextEl ? contextEl.textContent.trim() : "";

            if (message) {
                messages.push({
                    level: level,
                    message: message,
                    context: context
                });
            }
        });
        return messages.length > 0 ? messages : null;
    }

    function extractGenericData(panel) {
        // Try to extract any table data
        const tables = panel.querySelectorAll("table");
        if (tables.length > 0) {
            const tableData = [];
            tables.forEach(table => {
                const rows = table.querySelectorAll("tr");
                const tableRows = [];
                rows.forEach(row => {
                    const cells = row.querySelectorAll("td, th");
                    const rowData = [];
                    cells.forEach(cell => {
                        rowData.push(cell.textContent.trim());
                    });
                    if (rowData.length > 0) {
                        tableRows.push(rowData);
                    }
                });
                if (tableRows.length > 0) {
                    tableData.push(tableRows);
                }
            });
            return tableData.length > 0 ? tableData : null;
        }

        // Try to extract list data
        const lists = panel.querySelectorAll("ul, ol");
        if (lists.length > 0) {
            const listData = [];
            lists.forEach(list => {
                const items = list.querySelectorAll("li");
                const listItems = [];
                items.forEach(item => {
                    listItems.push(item.textContent.trim());
                });
                if (listItems.length > 0) {
                    listData.push(listItems);
                }
            });
            return listData.length > 0 ? listData : null;
        }

        return null;
    }

    function extractEventsData(panel) {
        const events = [];

        // Try to extract from the events list
        const eventsList = panel.querySelector(".phpdebugbar-widgets-list");
        if (eventsList) {
            const eventItems = eventsList.querySelectorAll(".phpdebugbar-widgets-list-item");
            eventItems.forEach(item => {
                const eventText = item.textContent.trim();
                if (eventText) {
                    // Parse different event formats
                    if (eventText.includes(": ")) {
                        const parts = eventText.split(": ");
                        events.push({
                            event_type: parts[0].trim(),
                            event_target: parts.slice(1).join(": ").trim()
                        });
                    } else {
                        events.push({
                            event_description: eventText
                        });
                    }
                }
            });
        }

        // Also try to extract from any table data (summary statistics)
        const tables = panel.querySelectorAll("table");
        const summaryStats = [];
        tables.forEach(table => {
            const rows = table.querySelectorAll("tr");
            rows.forEach(row => {
                const cells = row.querySelectorAll("td, th");
                if (cells.length >= 2) {
                    const eventType = cells[0].textContent.trim();
                    const timing = cells[1].textContent.trim();
                    if (eventType && timing) {
                        summaryStats.push([eventType, timing]);
                    }
                }
            });
        });

        // Try to extract from plain text content if no structured data found
        if (events.length === 0 && summaryStats.length === 0) {
            const textContent = panel.textContent.trim();
            if (textContent) {
                const lines = textContent.split("\\n").filter(line => line.trim());
                lines.forEach(line => {
                    line = line.trim();
                    if (line.includes(": ")) {
                        const parts = line.split(": ");
                        events.push({
                            event_type: parts[0].trim(),
                            event_target: parts.slice(1).join(": ").trim()
                        });
                    } else if (line.includes(" x ") && line.includes("(") && line.includes("%")) {
                        // This looks like a summary statistic
                        const parts = line.split("\\t").filter(p => p.trim());
                        if (parts.length >= 2) {
                            summaryStats.push([parts[0].trim(), parts[1].trim()]);
                        }
                    }
                });
            }
        }

        const result = {};
        if (events.length > 0) {
            result.individual_events = events;
        }
        if (summaryStats.length > 0) {
            result.summary_statistics = summaryStats;
        }
        if (events.length > 0 || summaryStats.length > 0) {
            result.total_events = events.length;
            return result;
        }

        return null;
    }

    function extractAllData() {
        const data = {
            timestamp: new Date().toISOString(), 
            url: location.href, 
            panels: {}
        };
        document.querySelectorAll(".phpdebugbar-tab").forEach(tab => {
            const name = tab.getAttribute("data-collector");
            if (name && name !== "__datasets") {
                data.panels[name] = extractPanelData(name);
            }
        });
        return data;
    }

    function init() {
        // Wait for debugbar to be ready
        if (!document.querySelector(".phpdebugbar")) {
            return setTimeout(init, 100);
        }

        // Add copy buttons to panels
        document.querySelectorAll(".phpdebugbar-panel").forEach(panel => {
            const name = panel.getAttribute("data-collector");
            if (!name || name === "__datasets") return;

            // Check if button already exists
            if (panel.querySelector(".phpdebugbar-copy-btn")) return;

            let toolbar = panel.querySelector(".phpdebugbar-widgets-status, .phpdebugbar-widgets-toolbar");
            if (!toolbar) {
                toolbar = document.createElement("div");
                toolbar.style.cssText = "padding:5px 10px;background:#f5f5f5;border-bottom:1px solid #ddd;";
                panel.insertBefore(toolbar, panel.firstChild);
            }

            const btn = document.createElement("button");
            btn.textContent = "Copy"; 
            btn.title = `Copy ${name} data`;
            btn.className = "phpdebugbar-copy-btn";
            btn.onclick = async () => {
                const data = JSON.stringify(extractPanelData(name), null, 2);
                const success = await copyToClipboard(data);
                showFeedback(btn, success);
            };
            toolbar.appendChild(btn);
        });

        // Add Copy All button
        const headerRight = document.querySelector(".phpdebugbar-header-right");
        if (headerRight && !headerRight.querySelector(".phpdebugbar-copy-all-btn")) {
            const btn = document.createElement("button");
            btn.textContent = "Copy All"; 
            btn.title = "Copy all debugbar data";
            btn.className = "phpdebugbar-copy-all-btn";
            btn.onclick = async () => {
                const data = JSON.stringify(extractAllData(), null, 2);
                const success = await copyToClipboard(data);
                showFeedback(btn, success);
            };
            headerRight.insertBefore(btn, headerRight.firstChild);
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", init);
    } else {
        init();
    }
})();
</script>';
    }
}
